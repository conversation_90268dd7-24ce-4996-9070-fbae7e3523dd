Master Technical Design Document: AI-Powered Schema Mapping FactoryVersion: 5.0 (Master Blueprint)Date: July 28, 2025Target Tech Stack: Python, Azure Functions / Python Scripts, Azure OpenAI Service, Kusto (Azure Data Explorer), Streamlit, Git1.0 Mission & ObjectiveMission: To create a semi-autonomous, scalable factory that ingests third-party (3P) security data sources, intelligently maps their schemas to corresponding first-party (1P) Microsoft Defender schemas, and generates validated, production-ready normalization configurations.Primary Objective: To reduce the time required to onboard a new 3P data source from weeks to days by automating the most labor-intensive analysis and mapping tasks, using a combination of AI agents and a human-in-the-loop (HITL) validation process.2.0 High-Level ArchitectureThe system is composed of three sequential phases, orchestrated by a central workflow manager (e.g., Azure Logic Apps triggering Azure Functions, or a master Python script).Phase 1: Universal Profiling Engine: Ingests and analyzes a table's schema to build a rich, reusable knowledge base.Phase 2: Automated Mapping Workflow: Uses the knowledge base to intelligently generate candidate mappings and transformation logic.Phase 3: HITL & Reinforcement: Presents AI-generated mappings to human analysts for validation and uses their feedback to continuously improve the system's core knowledge.3.0 Core Data Schemas (Kusto Tables)Two central Kusto tables will be created to store the system's state and knowledge.3.1 SchemaInsights TableThis is the permanent, reusable knowledge base containing detailed profiles of every analyzed column.// KQL for creating the central knowledge base
.create table SchemaInsights (
    TableName: string,
    ColumnName: string,           // For virtual columns, this will be the flattened name e.g., "AdditionalDetails_IPAddress"
    SourcePath: string,           // For virtual columns, the original JSON path e.g., "AdditionalDetails.IPAddress"
    IsExpanded: bool,             // TRUE if this is a virtual column from a dynamic field
    KustoDataType: string,
    SemanticDescription: string,  // Generated by LLM
    LogicalCategory: string,      // Generated by LLM
    EntityType: string,           // Generated by LLM: User, Device, IPAddress, URL, File, Process, Email, Application, GeoLocation, CloudResource, Other, None
    DistinctValueSamples: dynamic,// JSON object of top 50 values and their counts
    DescriptionEmbedding: dynamic,// Vector embedding of the description + name + category
    LastUpdated: datetime
)
3.2 MappingCandidates TableThis is a transactional table that tracks the mapping process for each new source.// KQL for creating the transactional mapping table
.create table MappingCandidates (
    RunId: string,                 // A unique ID for each mapping job
    SourceTableName: string,
    TargetTableName: string,
    SourceColumn: string,
    CandidateTargetColumn: string,
    EmbeddingSimilarityScore: real,
    SuggestedMappingType: string, // Generated by Agent: Direct, CastNeeded, ValueMapNeeded, Expression
    GeneratedYAML: string,         // Generated by Agent
    Status: string,                // PendingInternalReview, PendingHITL, Approved, Rejected, Corrected
    ReviewerComments: string,
    LastUpdated: datetime
)
4.0 Phase 1: Universal Profiling Engine (The Knowledge Base Builder)Implementation: A master Python script (profiler_main.py) that orchestrates calls to the functions below. This can be run manually or triggered by a pipeline.Step 1.1: Dynamic Column ExpansionObjective: Discover and flatten all nested fields within dynamic type columns.Python Function Definition:def expand_dynamic_columns(kusto_client, openai_client, table_name: str) -> list[dict]:
    """Identifies dynamic columns and extracts their nested schema."""
    pass
Pseudocode / Implementation Logic:function expand_dynamic_columns(kusto_client, openai_client, table_name):
    dynamic_columns = kusto_client.execute("table_name | getschema | where CSLDataType == 'dynamic' | project ColumnName")
    all_virtual_columns = []

    for col in dynamic_columns:
        # Get sample data for LLM inference
        sample_json_data = kusto_client.execute("table_name | take 100 | where isnotempty(col) | project col")

        # LLM Implementation Placeholder
        # Construct prompt as specified in v4.0 doc
        # Make API call to Azure OpenAI 'gpt-4o' model
        flattened_paths = openai_client.chat.completions.create(...) 

        for path in flattened_paths:
            virtual_col_info = {
                "ColumnName": f"{col}_{path.replace('.', '_')}",
                "SourcePath": f"{col}.{path}",
                "IsExpanded": True
            }
            all_virtual_columns.append(virtual_col_info)

    return all_virtual_columns
Step 1.2: Column Data ProfilingObjective: Extract statistical and sample data for every real and virtual column.Python Function Definition:def profile_column(kusto_client, table_name: str, column_info: dict) -> dict:
    """Generates a data profile for a single real or virtual column."""
    pass
Pseudocode / Implementation Logic:function profile_column(kusto_client, table_name, column_info):
    if column_info['IsExpanded']:
        query = f"{table_name} | extend TempCol = tostring({column_info['SourcePath']}) | summarize ..."
    else:
        query = f"{table_name} | summarize ..."

    # Kusto query as defined in v4.0 doc to get dcount and top 50 samples
    profile_results = kusto_client.execute(query)

    return {
        "column_name": column_info['ColumnName'],
        "data_type": profile_results['DataType'], # Simplified, get from getschema
        "distinct_value_samples": profile_results['Samples']
    }
Step 1.3: Semantic Insight, Category, & Entity GenerationObjective: Use an LLM, augmented with optional vendor documentation, to generate a description, category, and entity type for each column.Python Function Definition:def generate_semantic_insights(
    openai_client, 
    column_profile: dict, 
    vendor_description: str | None = None
) -> dict:
    """Uses an LLM to generate semantic metadata for a column, optionally using a vendor-provided description for context."""
    pass
Pseudocode / Implementation Logic:function generate_semantic_insights(openai_client, column_profile, vendor_description=None):
    # Base prompt construction
    base_prompt = f"""
    You are a senior security data architect. Analyze the following column profile and return a JSON object with three keys: "semantic_description", "logical_category", and "entity_type".

    Column Profile:
    {json.dumps(column_profile, indent=2)}
    """

    # Conditionally add the vendor context to the prompt
    if vendor_description:
        contextual_prompt = f"""
        {base_prompt}

        For additional context, the vendor provides the following description for this column: "{vendor_description}"
        Use this vendor description to improve the accuracy of your generated insights.
        """
    else:
        contextual_prompt = base_prompt

    final_prompt = f"""
    {contextual_prompt}

    Predefined Entity Types: [User, Device, IPAddress, URL, File, Process, Email, Application, GeoLocation, CloudResource, Other, None]

    Your response must be a single, valid JSON object only.
    """

    # LLM Implementation Placeholder
    llm_response = openai_client.chat.completions.create(prompt=final_prompt, ...)

    insights = json.loads(llm_response.choices[0].message.content)

    return insights
Step 1.4: Embedding GenerationObjective: Create a vector embedding for each column to enable semantic search.Python Function Definition:def generate_embedding(
    openai_client, 
    column_name: str, 
    insights: dict, 
    vendor_description: str | None = None
) -> list[float]:
    """Generates a vector embedding for a column's semantic profile."""
    pass
Pseudocode / Implementation Logic:function generate_embedding(openai_client, column_name, insights, vendor_description=None):
    # Construct a rich string for embedding
    text_to_embed = f"Column: {column_name}; Category: {insights['logical_category']}; Entity: {insights['entity_type']}; Description: {insights['semantic_description']}"

    if vendor_description:
        text_to_embed += f"; Vendor Context: {vendor_description}"

    # LLM Implementation Placeholder
    embedding_response = openai_client.embeddings.create(input=text_to_embed, ...)

    return embedding_response.data[0].embedding
Step 1.5: Store in Knowledge BaseObjective: Persist all generated insights to the SchemaInsights Kusto table.Python Function Definition:def store_insights_to_kusto(kusto_client, full_profile_data: list[dict]):
    """Ingests a list of full column profiles into the SchemaInsights table."""
    pass
Pseudocode / Implementation Logic:function store_insights_to_kusto(kusto_client, full_profile_data):
    # Convert the list of dictionaries to a pandas DataFrame
    df = pandas.DataFrame(full_profile_data)

    # Use the Azure Kusto Python SDK's ingest client
    kusto_ingest_client.ingest_from_dataframe(df, ...)
5.0 Phase 2: Automated Mapping Workflow (The Matching Engine)Implementation: A master Python script (mapper_main.py) or agent that is triggered when a new 3P source is profiled.Step 2.1: Initial Candidate MatchingObjective: Find the most likely mapping candidates using vector similarity.Python Function Definition:def find_candidate_matches(kusto_client, source_table: str, target_table: str, top_n: int = 3) -> list[dict]:
    """Finds top N mapping candidates based on embedding similarity."""
    pass
Pseudocode / Implementation Logic:function find_candidate_matches(kusto_client, source_table, target_table, top_n):
    # Kusto query using series_cosine_similarity() as defined in v4.0 doc
    # The query should join SchemaInsights on itself to compare source table rows with target table rows
    query = f"""
    let source_columns = SchemaInsights | where TableName == '{source_table}';
    let target_columns = SchemaInsights | where TableName == '{target_table}';
    source_columns | extend dummy=1 | join (target_columns | extend dummy=1) on dummy
    | project SourceColumn=ColumnName, TargetColumn=ColumnName1, Similarity=series_cosine_similarity(DescriptionEmbedding, DescriptionEmbedding1)
    | top-nested {top_n} by Similarity desc per SourceColumn
    """
    candidate_pairs = kusto_client.execute(query)

    return candidate_pairs
Step 2.2: Deep Analysis & Transformation Logic Generation (MappingAnalysisAgent)Objective: For each high-confidence candidate pair, determine the exact transformation logic.Python Function Definition:def generate_transformation_logic(openai_client, kusto_client, source_column: str, target_column: str) -> str:
    """Generates a YAML block for a single source-to-target mapping."""
    pass
Pseudocode / Implementation Logic:function generate_transformation_logic(openai_client, kusto_client, source_column, target_column):
    # Retrieve full profiles for both columns from SchemaInsights table
    source_profile = kusto_client.execute(f"SchemaInsights | where ColumnName == '{source_column}'")
    target_profile = kusto_client.execute(f"SchemaInsights | where ColumnName == '{target_column}'")

    # LLM Implementation Placeholder 1: Determine Mapping Type
    # Construct prompt for mapping type as specified in v4.0 doc
    mapping_type_response = openai_client.chat.completions.create(...)
    mapping_type = json.loads(mapping_type_response.choices[0].message.content)['mapping_type']

    value_map = None
    if mapping_type == "ValueMapNeeded":
        # LLM Implementation Placeholder 2: Map Distinct Values
        # Construct prompt for value mapping as specified in v4.0 doc
        value_map_response = openai_client.chat.completions.create(...)
        value_map = json.loads(value_map_response.choices[0].message.content)

    # Assemble the final YAML string based on mapping_type and value_map
    yaml_string = assemble_yaml(source_column, target_column, mapping_type, value_map)

    return yaml_string
Step 2.3: Store Candidate MappingsObjective: Persist the AI-generated mapping and its logic for human review.Python Function Definition:def store_candidate_mappings(kusto_client, candidate_data: list[dict]):
    """Ingests a list of generated mapping candidates into the MappingCandidates table."""
    pass
Pseudocode / Implementation Logic:function store_candidate_mappings(kusto_client, candidate_data):
    # Similar to Step 1.5, convert list of dicts to a pandas DataFrame
    # Ingest the DataFrame into the MappingCandidates table
    # Ensure 'Status' is set to 'PendingHITL'
6.0 Phase 3: HITL & Reinforcement (The Validation & Learning Layer)Step 3.1: HITL Validation (Streamlit UI)Objective: Allow human analysts to approve, correct, or reject AI-generated mappings.Implementation: A Streamlit web application (app.py).Backend Functions Called by Streamlit:get_pending_reviews(kusto_client, run_id): Queries MappingCandidates for Status == 'PendingHITL'.update_review_status(kusto_client, run_id, source_column, new_status, comments, corrected_target=None): Updates the Kusto table based on user action.Step 3.2: Reinforcement Learning Feedback (FeedbackAgent)Objective: Use human corrections to improve the core knowledge base and immediately fix the current mapping job's generated logic.Implementation: An Azure Function or Python script triggered by a Kusto data export or a Logic App watching for Status == 'Corrected' in the MappingCandidates table.Python Function Definition:def apply_reinforcement_feedback(openai_client, kusto_client, corrected_mapping_info: dict):
    """
    Updates both the current mapping job and the long-term knowledge base based on human correction.
    This is a two-part process:
    1. Correct the immediate output (YAML in MappingCandidates).
    2. Improve the long-term memory (insights in SchemaInsights).
    """
    pass
Pseudocode / Implementation Logic:function apply_reinforcement_feedback(openai_client, kusto_client, corrected_mapping_info):
    source_column = corrected_mapping_info['SourceColumn']
    human_corrected_target_column = corrected_mapping_info['CorrectedTargetColumn']
    run_id = corrected_mapping_info['RunId']

    # --- PART 1: IMMEDIATE CORRECTION of the current mapping job ---
    print("Correcting current mapping...")

    # Re-run the logic generation with the human-provided correct target
    # This reuses the function from Phase 2, Step 2.2
    corrected_yaml = generate_transformation_logic(
        openai_client, 
        kusto_client, 
        source_column, 
        human_corrected_target_column
    )

    # Update the MappingCandidates table with the corrected logic and set status to 'Approved'
    kusto_client.execute(
        f""".update table MappingCandidates on RunId, SourceColumn
           set CandidateTargetColumn = '{human_corrected_target_column}', 
               GeneratedYAML = '{corrected_yaml}', 
               Status = 'Approved', 
               ReviewerComments = 'Corrected by HITL'
           where RunId == '{run_id}' and SourceColumn == '{source_column}'"""
    )

    # --- PART 2: LONG-TERM LEARNING to prevent future mistakes ---
    print("Updating long-term knowledge base...")

    # LLM Implementation Placeholder: Ask LLM to regenerate insights based on the correction
    # This prompt is key to the learning process.
    correction_context_prompt = f"""
    A human expert has corrected a mapping. The original source column was '{source_column}'.
    Your previous AI suggestion was incorrect. The expert has correctly mapped it to '{human_corrected_target_column}'.
    Please generate an improved `semantic_description`, `logical_category`, and `entity_type` for the source column '{source_column}' that reflects this expert correction. This will be used to improve future suggestions.
    """
    improved_insights_response = openai_client.chat.completions.create(prompt=correction_context_prompt, ...)
    improved_insights = json.loads(improved_insights_response.choices[0].message.content)

    # Generate a new, more accurate embedding for the source column
    new_embedding = generate_embedding(
        openai_client, 
        source_column, 
        improved_insights
    )

    # Update the SchemaInsights table for the source_column with the new, improved metadata
    kusto_client.execute(
        f""".update table SchemaInsights on ColumnName
           set SemanticDescription = '{improved_insights['semantic_description']}',
             LogicalCategory = '{improved_insights['logical_category']}',
             EntityType = '{improved_insights['entity_type']}',
             DescriptionEmbedding = dynamic({new_embedding})
         where ColumnName == '{source_column}'"""
    )

    print("Feedback loop complete.")
Step 3.3: Finalize and DeployObjective: Commit the fully approved mapping configuration to Git.Implementation: An automated workflow (e.g., GitHub Action).Python Function Definition:def generate_and_commit_final_yaml(kusto_client, git_repo_path: str, run_id: str):
    """Assembles the final YAML file and commits it to a Git repository."""
    pass
Pseudocode / Implementation Logic:function generate_and_commit_final_yaml(kusto_client, git_repo_path, run_id):
    # Query MappingCandidates for all 'Approved' rows for the run_id
    approved_mappings = kusto_client.execute(...)

    final_yaml_content = assemble_full_yaml(approved_mappings)

    # Use a library like GitPython to perform git operations
    # Commit the final YAML file to the designated repository
